{"name": "@intlify/message-compiler", "version": "9.14.4", "description": "@intlify/message-compiler", "keywords": ["compiler", "i18n", "internationalization", "intlify", "message-format"], "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/intlify/vue-i18n/tree/master/packages/message-compiler#readme", "repository": {"type": "git", "url": "git+https://github.com/intlify/vue-i18n.git", "directory": "packages/message-compiler"}, "bugs": {"url": "https://github.com/intlify/vue-i18n/issues"}, "files": ["index.js", "dist"], "main": "index.js", "module": "dist/message-compiler.mjs", "unpkg": "dist/message-compiler.global.js", "jsdelivr": "dist/message-compiler.global.js", "types": "dist/message-compiler.d.ts", "dependencies": {"source-map-js": "^1.0.2", "@intlify/shared": "9.14.4"}, "engines": {"node": ">= 16"}, "buildOptions": {"name": "IntlifyMessageCompiler", "formats": ["mjs", "mjs-node", "browser", "cjs", "global"], "enableFullBundleForEsmBrowser": true}, "exports": {".": {"types": "./dist/message-compiler.d.ts", "browser": "./dist/message-compiler.esm-browser.js", "node": {"import": {"production": "./dist/message-compiler.node.mjs", "development": "./dist/message-compiler.node.mjs", "default": "./dist/message-compiler.node.mjs"}, "require": {"production": "./dist/message-compiler.prod.cjs", "development": "./dist/message-compiler.cjs", "default": "./index.js"}}, "import": "./dist/message-compiler.mjs"}, "./dist/*": "./dist/*", "./package.json": "./package.json"}, "funding": "https://github.com/sponsors/kazupon", "publishConfig": {"access": "public"}, "sideEffects": false}